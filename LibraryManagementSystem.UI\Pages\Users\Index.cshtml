@page
@model LibraryManagementSystem.UI.Pages.Users.IndexModel
@{
    ViewData["Title"] = "المستخدمون - Users";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-users"></i>
                        إدارة المستخدمين - Users Management
                    </h3>
                    <a href="/Auth/Register" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        إضافة مستخدم جديد - Add New User
                    </a>
                </div>
                <div class="card-body">
                    <!-- رسائل التنبيه - Alert Messages -->
                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @Model.ErrorMessage
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @Model.SuccessMessage
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (Model.Users?.Items?.Any() == true)
                    {
                        <!-- معلومات الصفحة - Page Information -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h5>قائمة المستخدمين - Users List</h5>
                                <p class="text-muted">
                                    عرض @Model.Users.Items.Count من أصل @Model.Users.TotalCount مستخدم
                                    (الصفحة @Model.Users.PageNumber من @Model.Users.TotalPages)
                                </p>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الرقم - ID</th>
                                        <th>الاسم الكامل - Full Name</th>
                                        <th>البريد الإلكتروني - Email</th>
                                        <th>رقم الهاتف - Phone</th>
                                        <th>العنوان - Address</th>
                                        <th>تاريخ العضوية - Membership Date</th>
                                        <th>الحالة - Status</th>
                                        <th>الإجراءات - Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in Model.Users.Items)
                                    {
                                        <tr>
                                            <td>@user.UserId</td>
                                            <td>@user.FirstName @user.LastName</td>
                                            <td>@user.Email</td>
                                            <td>@user.PhoneNumber</td>
                                            <td>@user.Address</td>
                                            <td>@user.MembershipDate.ToString("yyyy-MM-dd")</td>
                                            <td>
                                                @if (user.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط - Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط - Inactive</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="/Users/<USER>/@user.UserId" class="btn btn-info btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                        عرض - View
                                                    </a>
                                                    <a href="/Users/<USER>/@user.UserId" class="btn btn-warning btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                        تعديل - Edit
                                                    </a>
                                                    <button type="button" class="btn btn-danger btn-sm"
                                                        onclick="confirmDeleteUser(@user.UserId, '@user.FirstName @user.LastName')">
                                                        <i class="fas fa-trash"></i>
                                                        حذف - Delete
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- التنقل بين الصفحات - Pagination -->
                        @if (Model.Users.TotalPages > 1)
                        {
                            <nav class="mt-4">
                                <ul class="pagination justify-content-center">
                                    @if (Model.Users.HasPreviousPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@GetPageUrl(Model.Users.PageNumber - 1)">السابق</a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, Model.Users.PageNumber - 2);
                                                                i <= Math.Min(Model.Users.TotalPages, Model.Users.PageNumber + 2);
                                                                i++)
                                    {
                                        <li class="page-item @(i == Model.Users.PageNumber ? "active" : "")">
                                            <a class="page-link" href="@GetPageUrl(i)">@i</a>
                                        </li>
                                    }

                                    @if (Model.Users.HasNextPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@GetPageUrl(Model.Users.PageNumber + 1)">التالي</a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i>
                            لا توجد مستخدمون مسجلون حالياً - No users registered yet
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    private string GetPageUrl(int pageNumber)
    {
        return $"/Users?pageNumber={pageNumber}&pageSize={Model.PageSize}";
    }
}

@section Scripts {
    <script>
        // تأكيد حذف المستخدم
        // Confirm user deletion
        function confirmDeleteUser(userId, userName) {
            if (confirm(`هل أنت متأكد من حذف المستخدم: ${userName}؟\n\nهذا الإجراء لا يمكن التراجع عنه.\n\nAre you sure you want to delete the user: ${userName}?\n\nThis action cannot be undone.`)) {
                // إرسال طلب حذف
                fetch(`/Users/<USER>/${userId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('تم حذف المستخدم بنجاح - User deleted successfully');
                            location.reload();
                        } else {
                            alert('فشل في حذف المستخدم: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ أثناء حذف المستخدم - An error occurred while deleting the user');
                    });
            }
        }
    </script>
}
