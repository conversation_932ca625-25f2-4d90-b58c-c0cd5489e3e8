﻿using LibraryManagementSystem.BLL.Services;
using LibraryManagementSystem.BLL.Validation;
using LibraryManagementSystem.DAL.Caching;
using LibraryManagementSystem.DAL.Data;
using LibraryManagementSystem.DAL.Repositories;
using LibraryManagementSystem.DAL.UnitOfWork;
using LibraryManagementSystem.UI.Middleware;
using LibraryManagementSystem.UI.Extensions;
using Microsoft.AspNetCore.Mvc;

namespace LibraryManagementSystem.UI
{
    /// <summary>
    /// نقطة دخول التطبيق الرئيسية
    /// Main application entry point
    /// </summary>
    public class Program
    {
        /// <summary>
        /// الدالة الرئيسية لبدء التطبيق
        /// Main function to start the application
        /// </summary>
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // إضافة الخدمات إلى الحاوية
            // Add services to the container
            ConfigureServices(builder.Services, builder.Configuration);

            var app = builder.Build();

            // تهيئة قاعدة البيانات
            // Initialize database
            InitializeDatabase(app);

            // تكوين pipeline طلبات HTTP
            // Configure the HTTP request pipeline
            ConfigurePipeline(app);

            app.Run();
        }

        /// <summary>
        /// تكوين الخدمات
        /// Configure services
        /// </summary>
        private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            // إضافة خدمات Session
            // Add Session services
            services.AddDistributedMemoryCache();
            services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromMinutes(30);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
                options.Cookie.Name = "LibraryManagement.Session";
            });

            // إضافة Razor Pages
            // Add Razor Pages
            services.AddRazorPages(options =>
            {
                options.Conventions.ConfigureFilter(new IgnoreAntiforgeryTokenAttribute());
            });

            // إضافة Controllers للـ API
            // Add Controllers for API
            services.AddControllers();

            // إضافة خدمات التخزين المؤقت
            // Add caching services
            services.AddMemoryCache(options =>
            {
                options.SizeLimit = 1000; // حد أقصى 1000 عنصر
                options.CompactionPercentage = 0.25; // ضغط 25% عند الوصول للحد الأقصى
            });
            services.AddMemoryCache(); // enables IMemoryCache

            services.AddScoped<ICacheService, MemoryCacheService>();

            // إضافة خدمات قاعدة البيانات
            // Add database services
            var connectionString = configuration.GetConnectionString("DefaultConnection")
                ?? "Server=(localdb)\\mssqllocaldb;Database=LibraryManagementSystem;Trusted_Connection=true;MultipleActiveResultSets=true";

            services.AddScoped<IDatabaseConnectionFactory>(provider =>
                new DatabaseConnectionFactory(connectionString));

            // إضافة خدمة تهيئة قاعدة البيانات
            // Database initialization is handled by DatabaseConnectionFactory

            // إضافة المستودعات
            // Add repositories
            services.AddScoped<IBookRepository, BookRepository>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IBorrowingRepository, BorrowingRepository>();

            // إضافة وحدة العمل
            // Add Unit of Work
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // إضافة خدمات منطق الأعمال
            // Add business logic services
            services.AddScoped<IBookService, BookService>();
            services.AddScoped<IBorrowingService, BorrowingService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IAuthenticationService, AuthenticationService>();
            services.AddScoped<IAuthorizationService, AuthorizationService>();

            // إضافة خدمات التحقق من قواعد الأعمال
            // Add business rule validation services
            services.AddScoped<IBusinessRuleValidator, BusinessRuleValidator>();

            // إضافة خدمات JWT
            // Add JWT services
            services.Configure<JwtSettings>(configuration.GetSection("JwtSettings"));
            services.AddScoped<IJwtService, JwtService>();

            // إضافة إعدادات المكتبة
            // Add library settings
            services.Configure<LibrarySettings>(configuration.GetSection("LibrarySettings"));

            // إضافة خدمات التسجيل
            // Add logging services
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
            });

            // إضافة خدمات HTTP Context
            // Add HTTP Context services
            services.AddHttpContextAccessor();

            // إضافة خدمات التحقق من صحة النموذج
            // Add model validation services
            services.AddAntiforgery(options =>
            {
                options.HeaderName = "X-CSRF-TOKEN";
            });

            // إضافة معالجة الأخطاء العامة
            // Add global error handling
            services.AddGlobalErrorHandling();

            // إضافة معالجة أخطاء قاعدة البيانات
            // Add database error handling
            services.AddDatabaseErrorHandling(connectionString);
        }

        /// <summary>
        /// تهيئة قاعدة البيانات
        /// Initialize database
        /// </summary>
        private static void InitializeDatabase(WebApplication app)
        {
            try
            {
                using var scope = app.Services.CreateScope();
                var connectionFactory = scope.ServiceProvider.GetRequiredService<IDatabaseConnectionFactory>();
                var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

                logger.LogInformation("بدء تهيئة قاعدة البيانات - Starting database initialization");

                // تشغيل تهيئة قاعدة البيانات بشكل متزامن عند بدء التطبيق
                // Run database initialization synchronously at application startup
                connectionFactory.InitializeDatabaseAsync().GetAwaiter().GetResult();

                logger.LogInformation("تم إكمال تهيئة قاعدة البيانات بنجاح - Database initialization completed successfully");
            }
            catch (Exception ex)
            {
                var logger = app.Services.GetRequiredService<ILogger<Program>>();
                logger.LogError(ex, "فشل في تهيئة قاعدة البيانات - Failed to initialize database");
                throw;
            }
        }

        /// <summary>
        /// تكوين pipeline المعالجة
        /// Configure processing pipeline
        /// </summary>
        private static void ConfigurePipeline(WebApplication app)
        {
            // تكوين معالجة الأخطاء العامة
            // Configure global error handling
            app.UseGlobalErrorHandling();

            // تكوين معالجة الأخطاء
            // Configure error handling
            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Error");
                app.UseHsts();
            }
            else
            {
                app.UseDeveloperExceptionPage();
            }

            // إعادة توجيه HTTPS
            // HTTPS redirection
            app.UseHttpsRedirection();

            // الملفات الثابتة
            // Static files
            app.UseStaticFiles();

            // التوجيه
            // Routing
            app.UseRouting();

            // الجلسة
            // Session
            app.UseSession();

            // التفويض
            // Authorization
            app.UseAuthorization();

            // تكوين Razor Pages
            // Configure Razor Pages
            app.MapRazorPages();

            // تكوين Controllers للـ API
            // Configure Controllers for API
            app.MapControllers();

            // إضافة مراقبة الصحة
            // Add health monitoring
            app.UseHealthMonitoring();

            // الصفحة الافتراضية
            // Default page
            app.MapGet("/", () => Results.Redirect("/Auth/Login"));
        }
    }
}
