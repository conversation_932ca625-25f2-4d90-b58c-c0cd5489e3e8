@page
@using LibraryManagementSystem.DAL.Models.Enums
@model LibraryManagementSystem.UI.Pages.Books.IndexModel
@{
    ViewData["Title"] = "البحث عن الكتب - Search Books";
}

<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>
                        البحث عن الكتب - Search Books
                    </h3>
                    @if (Model.IsAdmin)
                    {
                        <a href="/Books/Create" class="btn btn-light">
                            <i class="fas fa-plus me-1"></i>
                            إضافة كتاب جديد - Add New Book
                        </a>
                    }
                </div>
                <div class="card-body">
                    <!-- رسائل التنبيه - Alert Messages -->
@if (!string.IsNullOrEmpty(Model.ErrorMessage))
{
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @Model.ErrorMessage
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
}

@if (!string.IsNullOrEmpty(Model.SuccessMessage))
{
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @Model.SuccessMessage
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
}

                    <!-- نموذج البحث - Search Form -->
                    <form method="get" class="mb-4" id="searchForm">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="searchTerm" class="form-label">
                                    البحث - Search
                                    <small class="text-muted">(اختياري - optional)</small>
                                </label>
                                <input type="text" class="form-control" id="searchTerm" name="searchTerm"
                                       value="@Model.SearchCriteria.SearchTerm"
                                       placeholder="ابحث بالعنوان، المؤلف، أو الرقم المعياري... (اتركه فارغاً لعرض جميع الكتب)"
                                       title="ابحث بالعنوان، المؤلف، أو الرقم المعياري، أو اتركه فارغاً لعرض جميع الكتب">
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    البحث في العنوان، المؤلف، الرقم المعياري، والوصف - أو اتركه فارغاً لعرض جميع الكتب
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label for="genre" class="form-label">النوع - Genre</label>
                                <select class="form-select" id="genre" name="genre">
                                    <option value="">جميع الأنواع - All Genres</option>
                                    <option value="أدب" selected="@(Model.SearchCriteria.Genre == "أدب")">أدب - Literature</option>
                                    <option value="رواية" selected="@(Model.SearchCriteria.Genre == "رواية")">رواية - Novel</option>
                                    <option value="خيال علمي" selected="@(Model.SearchCriteria.Genre == "خيال علمي")">خيال علمي - Science Fiction</option>
                                    <option value="تاريخ" selected="@(Model.SearchCriteria.Genre == "تاريخ")">تاريخ - History</option>
                                    <option value="فكر" selected="@(Model.SearchCriteria.Genre == "فكر")">فكر - Philosophy</option>
                                    <option value="سيرة ذاتية" selected="@(Model.SearchCriteria.Genre == "سيرة ذاتية")">سيرة ذاتية - Biography</option>
                                    <option value="قصص قصيرة" selected="@(Model.SearchCriteria.Genre == "قصص قصيرة")">قصص قصيرة - Short Stories</option>
                                    <option value="إثارة" selected="@(Model.SearchCriteria.Genre == "إثارة")">إثارة - Thriller</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="availableOnly" class="form-label">التوفر - Availability</label>
                                <select class="form-select" id="availableOnly" name="availableOnly">
                                    <option value="false">جميع الكتب - All Books</option>
                                    <option value="true" selected="@Model.SearchCriteria.AvailableOnly">المتاحة فقط - Available Only</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="sortBy" class="form-label">ترتيب حسب - Sort By</label>
                                <select class="form-select" id="sortBy" name="sortBy">
                                    <option value="Title" selected="@(Model.SearchCriteria.SortBy == "Title")">العنوان - Title</option>
                                    <option value="Author" selected="@(Model.SearchCriteria.SortBy == "Author")">المؤلف - Author</option>
                                    <option value="PublicationYear" selected="@(Model.SearchCriteria.SortBy == "PublicationYear")">سنة النشر - Publication Year</option>
                                    <option value="Genre" selected="@(Model.SearchCriteria.SortBy == "Genre")">النوع - Genre</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary" id="searchButton">
                                        <i class="fas fa-search me-1"></i>
                                        بحث - Search
                                    </button>
                                </div>
                                <div class="form-text text-center mt-1">
                                    <small class="text-muted">
                                        <i class="fas fa-lightbulb me-1"></i>
                                        نصيحة: استخدم كلمات مفتاحية محددة
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <a href="/Books" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-times me-1"></i>
                                    مسح الفلاتر - Clear Filters
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- نتائج البحث - Search results -->
@if (Model.SearchResults != null && Model.SearchResults.Items.Any())
{
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h4>نتائج البحث</h4>
                                <p class="text-muted">
                                    عرض @Model.SearchResults.Items.Count من أصل @Model.SearchResults.TotalCount كتاب
                                    (الصفحة @Model.SearchResults.PageNumber من @Model.SearchResults.TotalPages)
                                </p>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="sortBy" id="sortTitle" value="Title"
                                           checked="@(Model.SearchCriteria.SortBy == "Title")" onchange="updateSort(this)">
                                    <label class="btn btn-outline-primary" for="sortTitle">ترتيب بالعنوان</label>

                                    <input type="radio" class="btn-check" name="sortBy" id="sortAuthor" value="Author"
                                           checked="@(Model.SearchCriteria.SortBy == "Author")" onchange="updateSort(this)">
                                    <label class="btn btn-outline-primary" for="sortAuthor">ترتيب بالمؤلف</label>

                                    <input type="radio" class="btn-check" name="sortBy" id="sortYear" value="PublicationYear"
                                           checked="@(Model.SearchCriteria.SortBy == "PublicationYear")" onchange="updateSort(this)">
                                    <label class="btn btn-outline-primary" for="sortYear">ترتيب بالسنة</label>
                                </div>
                            </div>
                        </div>

                        <!-- جدول النتائج - Results table -->
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>العنوان</th>
                                                <th>الرقم المعياري الدولي</th>
                                                <th>المؤلف</th>
                                                <th>النوع</th>
                                                <th>سنة النشر</th>
                                                <th>التوفر</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var book in Model.SearchResults.Items)
                                            {
                                                <tr>
                                                    <td>
                                                        <strong>@book.Title</strong>
                                                        @if (!string.IsNullOrEmpty(book.Description))
                                                        {
                                                            <br>
                                                
                                                            <small class="text-muted">@book.Description.Substring(0, Math.Min(100, book.Description.Length))...</small>
                                                        }
                                                    </td>
                                                    <td>
                                                        <strong>@book.ISBN</strong>
                                                       @*  @if (!string.IsNullOrEmpty(book.ISBN))
                                                        {
                                                            <br>
                                                            <small class="text-muted">
                                                                @book.ISBN.Substring(0, Math.Min(100, book.ISBN.Length))...
                                                            </small>
                                                        } *@
                                                    </td>
                                                    <td>@book.Author</td>
                                                    <td>
                                                        @if (!string.IsNullOrEmpty(book.Genre))
                                                        {
                                                            <span class="badge bg-secondary">@book.Genre</span>
                                                        }
                                                    </td>
                                                    <td>@book.PublicationYear</td>
                                                    <td>
                                                        <div class="d-flex flex-column">
                                                            <span class="@book.AvailabilityStatus.GetCssClass() mb-1">
                                                                <i class="@book.AvailabilityStatus.GetIcon() me-1"></i>
                                                                @book.AvailabilityStatusText
                                                            </span>
                                                            <small class="text-muted">
                                                                <i class="fas fa-info-circle me-1"></i>
                                                                @book.AvailableCopies من @book.TotalCopies متوفرة
                                                            </small>
                                                            @if (book.BorrowedCopies > 0)
                                                            {
                                                                <small class="text-warning">
                                                                    <i class="fas fa-user-clock me-1"></i>
                                                                    @book.BorrowedCopies نسخة مُعارة
                                                                </small>
                                                            }
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="/Books/Details/@book.BookId" class="btn btn-outline-info" title="عرض التفاصيل - View Details">
                                                                <i class="fas fa-eye me-1"></i>
                                                                تفاصيل
                                                            </a>
                                                            @if (book.IsAvailable)
                                                            {
                                                                <form method="post" asp-page-handler="BorrowBook" class="d-inline">
                                                                    <input type="hidden" name="bookId" value="@book.BookId" />
                                                                    <button type="submit" class="btn btn-primary"
                                                                            title="استعارة الكتاب - Borrow Book"
                                                                            onclick="return confirm('هل تريد استعارة الكتاب: @book.Title؟\n\nفترة الاستعارة: 14 يوم\n\nDo you want to borrow: @book.Title?\nBorrowing period: 14 days')">
                                                                        <i class="fas fa-book-reader me-1"></i>
                                                                        استعارة
                                                                    </button>
                                                                </form>
                                                            }
                                                            @if (Model.IsAdmin)
                                                            {
                                                                <a href="/Books/Edit/@book.BookId" class="btn btn-outline-warning" title="تعديل الكتاب - Edit Book">
                                                                    <i class="fas fa-edit me-1"></i>
                                                                    تعديل
                                                                </a>
                                                                <form method="post" asp-page-handler="DeleteBook" asp-route-bookId="@book.BookId"
                                                                      onsubmit="return confirm('هل أنت متأكد من حذف الكتاب: @book.Title ؟\n\nهذا الإجراء لا يمكن التراجع عنه.\n\nAre you sure you want to delete the book: @book.Title ?\n\nThis action cannot be undone.')">
                                                                    <button type="submit" class="btn btn-outline-danger" title="حذف الكتاب - Delete Book">
                                                                        <i class="fas fa-trash me-1"></i> حذف
                                                                    </button>
                                                                </form>

                                                            }
                                                            else
                                                            {
                                                                <button type="button" class="btn btn-secondary" disabled
                                                                        title="الكتاب غير متوفر للاستعارة - Book not available for borrowing">
                                                                    <i class="fas fa-ban me-1"></i>
                                                                    غير متوفر
                                                                </button>
                                                            }
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- التنقل بين الصفحات - Pagination -->
    @if (Model.SearchResults.TotalPages > 1)
    {
                            <nav class="mt-4">
                                <ul class="pagination justify-content-center">
                                    @if (Model.SearchResults.HasPreviousPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@GetPageUrl(Model.SearchResults.PageNumber - 1)">السابق</a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, Model.SearchResults.PageNumber - 2);
                                                                i <= Math.Min(Model.SearchResults.TotalPages, Model.SearchResults.PageNumber + 2);
                                                                i++)
                                    {
                                        <li class="page-item @(i == Model.SearchResults.PageNumber ? "active" : "")">
                                            <a class="page-link" href="@GetPageUrl(i)">@i</a>
                                        </li>
                                    }

                                    @if (Model.SearchResults.HasNextPage)
                                    {
                                        <li class="page-item">
                                            <a class="page-link" href="@GetPageUrl(Model.SearchResults.PageNumber + 1)">التالي</a>
                                        </li>
                                    }
                                </ul>
                            </nav>
    }
}
else if (Model.SearchResults != null)
{
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            لم يتم العثور على كتب تطابق معايير البحث المحددة.
                        </div>
}



@functions {
    private string GetPageUrl(int pageNumber)
    {
        var queryString = Request.QueryString.ToString();
        if (queryString.Contains("pageNumber="))
        {
            queryString = System.Text.RegularExpressions.Regex.Replace(queryString, @"pageNumber=\d+", $"pageNumber={pageNumber}");
        }
        else
        {
            queryString += (queryString.Contains("?") ? "&" : "?") + $"pageNumber={pageNumber}";
        }
        return Request.Path + queryString;
    }
}

@section Scripts {
                        <script>
                            // تحديث الترتيب
                            // Update sorting
                            function updateSort(element) {
                                const form = document.querySelector('form');
                                const sortInput = document.createElement('input');
                                sortInput.type = 'hidden';
                                sortInput.name = 'sortBy';
                                sortInput.value = element.value;
                                form.appendChild(sortInput);
                                form.submit();
                            }

                            // تأكيد حذف الكتاب
                            // Confirm book deletion
                            function confirmDeleteBook(bookId, bookTitle) {
                                if (confirm(`هل أنت متأكد من حذف الكتاب: ${bookTitle}؟\n\nهذا الإجراء لا يمكن التراجع عنه.\n\nAre you sure you want to delete the book: ${bookTitle}?\n\nThis action cannot be undone.`)) {
                                    // إرسال طلب حذف
                                                             fetch(`?handler=DeleteBook&bookId=${bookId}`, {
                                method: 'POST',
                                headers: {
                                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                                }
                            });
                                    .then(response => response.json())
                                    .then(data => {
                                        if (data.success) {
                                            alert('تم حذف الكتاب بنجاح - Book deleted successfully');
                                            location.reload();
                                        } else {
                                            alert('فشل في حذف الكتاب: ' + data.message);
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error:', error);
                                        alert('حدث خطأ أثناء حذف الكتاب - An error occurred while deleting the book');
                                    });
                                }
                            }




                        </script>
}
